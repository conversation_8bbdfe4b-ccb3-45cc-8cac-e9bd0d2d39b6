using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace HikvisionPlayground
{
    /// <summary>
    /// Hikvision ISAPI Client for Face Recognition Terminals
    /// Basic connection and authentication example
    /// </summary>
    public class HikvisionClient : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _username;
        private readonly string _password;
        private bool _disposed = false;

        /// <summary>
        /// Initialize the Hikvision client
        /// </summary>
        /// <param name="host">IP address or hostname of the device</param>
        /// <param name="username">Username for authentication</param>
        /// <param name="password">Password for authentication</param>
        /// <param name="port">Port number (default 80)</param>
        public HikvisionClient(string host, string username, string password, int port = 80)
        {
            _username = username;
            _password = password;
            _baseUrl = $"http://{host}:{port}";
            
            // Create HttpClient with digest authentication handler
            var handler = new HttpClientHandler()
            {
                Credentials = new System.Net.NetworkCredential(username, password)
            };
            
            _httpClient = new HttpClient(handler)
            {
                Timeout = TimeSpan.FromSeconds(10)
            };
            
            // Set default headers
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/xml");
        }

        /// <summary>
        /// Make HTTP request to the device
        /// </summary>
        /// <param name="method">HTTP method</param>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="content">Request content</param>
        /// <returns>HTTP response</returns>
        private async Task<HttpResponseMessage> MakeRequestAsync(HttpMethod method, string endpoint, HttpContent content = null)
        {
            var url = _baseUrl + endpoint;
            var request = new HttpRequestMessage(method, url);
            
            if (content != null)
            {
                request.Content = content;
            }
            
            try
            {
                var response = await _httpClient.SendAsync(request);
                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Request failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Test connection to the device
        /// </summary>
        /// <returns>True if connection successful, false otherwise</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var response = await MakeRequestAsync(HttpMethod.Get, "/ISAPI/System/deviceInfo");
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("Connection test successful!");
                    return true;
                }
                else
                {
                    Console.WriteLine($"Connection test failed. Status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Connection test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get device information
        /// </summary>
        /// <returns>Device info XML string or null if failed</returns>
        public async Task<string> GetDeviceInfoAsync()
        {
            try
            {
                var response = await MakeRequestAsync(HttpMethod.Get, "/ISAPI/System/deviceInfo");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("Device info retrieved successfully");
                    return content;
                }
                else
                {
                    Console.WriteLine($"Failed to get device info. Status: {response.StatusCode}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get device info: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get system capabilities
        /// </summary>
        /// <returns>Capabilities XML string or null if failed</returns>
        public async Task<string> GetSystemCapabilitiesAsync()
        {
            try
            {
                var response = await MakeRequestAsync(HttpMethod.Get, "/ISAPI/System/capabilities");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("System capabilities retrieved successfully");
                    return content;
                }
                else
                {
                    Console.WriteLine($"Failed to get capabilities. Status: {response.StatusCode}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get capabilities: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Get face database capabilities
        /// </summary>
        /// <returns>Face database capabilities XML string or null if failed</returns>
        public async Task<string> GetFaceDatabaseCapabilitiesAsync()
        {
            try
            {
                var response = await MakeRequestAsync(HttpMethod.Get, "/ISAPI/AccessControl/FaceDataRecord/capabilities");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("Face database capabilities retrieved successfully");
                    return content;
                }
                else
                {
                    Console.WriteLine($"Failed to get face database capabilities. Status: {response.StatusCode}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get face database capabilities: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Parse XML response and display formatted output
        /// </summary>
        /// <param name="xmlContent">XML content to parse</param>
        /// <param name="title">Title for the output</param>
        public void DisplayXmlInfo(string xmlContent, string title)
        {
            if (string.IsNullOrEmpty(xmlContent))
            {
                Console.WriteLine($"{title}: No data available");
                return;
            }

            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(xmlContent);
                
                Console.WriteLine($"\n{title}:");
                Console.WriteLine("=" + new string('=', title.Length));
                
                DisplayXmlNode(doc.DocumentElement, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to parse XML: {ex.Message}");
                Console.WriteLine($"Raw XML: {xmlContent}");
            }
        }

        /// <summary>
        /// Recursively display XML nodes
        /// </summary>
        /// <param name="node">XML node to display</param>
        /// <param name="indent">Indentation level</param>
        private void DisplayXmlNode(XmlNode node, int indent)
        {
            if (node == null) return;

            string indentStr = new string(' ', indent * 2);
            
            if (node.NodeType == XmlNodeType.Element)
            {
                if (node.HasChildNodes && node.FirstChild.NodeType == XmlNodeType.Text)
                {
                    Console.WriteLine($"{indentStr}{node.Name}: {node.InnerText}");
                }
                else
                {
                    Console.WriteLine($"{indentStr}{node.Name}:");
                    foreach (XmlNode child in node.ChildNodes)
                    {
                        DisplayXmlNode(child, indent + 1);
                    }
                }
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method
        /// </summary>
        /// <param name="disposing">Whether disposing</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _httpClient?.Dispose();
                }
                _disposed = true;
            }
        }
    }
}
