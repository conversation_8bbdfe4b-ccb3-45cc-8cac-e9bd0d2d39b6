#include "hikvision_client.h"
#include <iostream>
#include <sstream>
#include <curl/curl.h>

HikvisionClient::HikvisionClient(const std::string& host, const std::string& username, 
                               const std::string& password, int port)
    : m_host(host), m_username(username), m_password(password), m_port(port), m_curl(nullptr) {
    
    // Build base URL
    std::ostringstream oss;
    oss << "http://" << host << ":" << port;
    m_baseUrl = oss.str();
    
    // Initialize curl
    initializeCurl();
}

HikvisionClient::~HikvisionClient() {
    if (m_curl) {
        curl_easy_cleanup(m_curl);
    }
    curl_global_cleanup();
}

void HikvisionClient::initializeCurl() {
    // Initialize curl globally
    curl_global_init(CURL_GLOBAL_DEFAULT);
    
    // Create curl handle
    m_curl = curl_easy_init();
    
    if (m_curl) {
        setupAuthentication();
    }
}

void HikvisionClient::setupAuthentication() {
    if (!m_curl) return;
    
    // Set digest authentication
    curl_easy_setopt(m_curl, CURLOPT_HTTPAUTH, CURLAUTH_DIGEST);
    
    // Set username and password
    std::string userpass = m_username + ":" + m_password;
    curl_easy_setopt(m_curl, CURLOPT_USERPWD, userpass.c_str());
    
    // Set timeout
    curl_easy_setopt(m_curl, CURLOPT_TIMEOUT, 10L);
    
    // Set headers
    struct curl_slist* headers = nullptr;
    headers = curl_slist_append(headers, "Content-Type: application/xml");
    headers = curl_slist_append(headers, "Accept: application/xml");
    curl_easy_setopt(m_curl, CURLOPT_HTTPHEADER, headers);
}

size_t HikvisionClient::WriteCallback(void* contents, size_t size, size_t nmemb, ResponseData* response) {
    size_t totalSize = size * nmemb;
    response->data.append(static_cast<char*>(contents), totalSize);
    return totalSize;
}

HikvisionClient::ResponseData HikvisionClient::makeRequest(const std::string& method, 
                                                          const std::string& endpoint, 
                                                          const std::string& data) {
    ResponseData response;
    response.responseCode = 0;
    
    if (!m_curl) {
        std::cerr << "CURL not initialized" << std::endl;
        return response;
    }
    
    // Build full URL
    std::string url = m_baseUrl + endpoint;
    curl_easy_setopt(m_curl, CURLOPT_URL, url.c_str());
    
    // Set write callback
    curl_easy_setopt(m_curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(m_curl, CURLOPT_WRITEDATA, &response);
    
    // Set HTTP method
    if (method == "GET") {
        curl_easy_setopt(m_curl, CURLOPT_HTTPGET, 1L);
    } else if (method == "POST") {
        curl_easy_setopt(m_curl, CURLOPT_POST, 1L);
        if (!data.empty()) {
            curl_easy_setopt(m_curl, CURLOPT_POSTFIELDS, data.c_str());
        }
    }
    
    // Perform request
    CURLcode res = curl_easy_perform(m_curl);
    
    if (res != CURLE_OK) {
        std::cerr << "curl_easy_perform() failed: " << curl_easy_strerror(res) << std::endl;
        return response;
    }
    
    // Get response code
    curl_easy_getinfo(m_curl, CURLINFO_RESPONSE_CODE, &response.responseCode);
    
    return response;
}

bool HikvisionClient::testConnection() {
    std::cout << "Testing connection to " << m_baseUrl << std::endl;
    
    ResponseData response = makeRequest("GET", "/ISAPI/System/deviceInfo");
    
    if (response.responseCode == 200) {
        std::cout << "Connection test successful!" << std::endl;
        return true;
    } else {
        std::cout << "Connection test failed. Response code: " << response.responseCode << std::endl;
        return false;
    }
}

std::string HikvisionClient::getDeviceInfo() {
    std::cout << "Getting device information..." << std::endl;
    
    ResponseData response = makeRequest("GET", "/ISAPI/System/deviceInfo");
    
    if (response.responseCode == 200) {
        std::cout << "Device info retrieved successfully" << std::endl;
        return response.data;
    } else {
        std::cout << "Failed to get device info. Response code: " << response.responseCode << std::endl;
        return "";
    }
}

std::string HikvisionClient::getSystemCapabilities() {
    std::cout << "Getting system capabilities..." << std::endl;
    
    ResponseData response = makeRequest("GET", "/ISAPI/System/capabilities");
    
    if (response.responseCode == 200) {
        std::cout << "System capabilities retrieved successfully" << std::endl;
        return response.data;
    } else {
        std::cout << "Failed to get system capabilities. Response code: " << response.responseCode << std::endl;
        return "";
    }
}

std::string HikvisionClient::getFaceDatabaseCapabilities() {
    std::cout << "Getting face database capabilities..." << std::endl;
    
    ResponseData response = makeRequest("GET", "/ISAPI/AccessControl/FaceDataRecord/capabilities");
    
    if (response.responseCode == 200) {
        std::cout << "Face database capabilities retrieved successfully" << std::endl;
        return response.data;
    } else {
        std::cout << "Failed to get face database capabilities. Response code: " << response.responseCode << std::endl;
        return "";
    }
}
