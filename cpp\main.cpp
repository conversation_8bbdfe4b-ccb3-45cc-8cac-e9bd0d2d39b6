#include "hikvision_client.h"
#include <iostream>
#include <string>

int main() {
    // Configuration - Update these values for your device
    const std::string DEVICE_IP = "*************";  // Replace with your device IP
    const std::string USERNAME = "admin";            // Replace with your username
    const std::string PASSWORD = "password";         // Replace with your password
    const int PORT = 80;                            // Replace with your port if different
    
    std::cout << "=================================================" << std::endl;
    std::cout << "Hikvision Face Recognition Terminal Client (C++)" << std::endl;
    std::cout << "=================================================" << std::endl;
    
    try {
        // Create client instance
        HikvisionClient client(DEVICE_IP, USERNAME, PASSWORD, PORT);
        
        // Test connection
        std::cout << "\n1. Testing connection..." << std::endl;
        if (!client.testConnection()) {
            std::cout << "Connection failed! Please check your configuration." << std::endl;
            return 1;
        }
        
        // Get device information
        std::cout << "\n2. Getting device information..." << std::endl;
        std::string deviceInfo = client.getDeviceInfo();
        if (!deviceInfo.empty()) {
            std::cout << "Device Info XML:" << std::endl;
            std::cout << deviceInfo << std::endl;
        }
        
        // Get system capabilities
        std::cout << "\n3. Getting system capabilities..." << std::endl;
        std::string capabilities = client.getSystemCapabilities();
        if (!capabilities.empty()) {
            std::cout << "System Capabilities XML:" << std::endl;
            std::cout << capabilities << std::endl;
        }
        
        // Get face database capabilities
        std::cout << "\n4. Getting face database capabilities..." << std::endl;
        std::string faceCapabilities = client.getFaceDatabaseCapabilities();
        if (!faceCapabilities.empty()) {
            std::cout << "Face Database Capabilities XML:" << std::endl;
            std::cout << faceCapabilities << std::endl;
        }
        
        std::cout << "\nAll operations completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
