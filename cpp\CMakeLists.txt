cmake_minimum_required(VERSION 3.10)
project(HikvisionClient)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(CURL REQUIRED libcurl)

# Include directories
include_directories(${CURL_INCLUDE_DIRS})

# Add executable
add_executable(hikvision_client
    main.cpp
    hikvision_client.cpp
)

# Link libraries
target_link_libraries(hikvision_client ${CURL_LIBRARIES})

# Compiler flags
target_compile_options(hikvision_client PRIVATE ${CURL_CFLAGS_OTHER})
