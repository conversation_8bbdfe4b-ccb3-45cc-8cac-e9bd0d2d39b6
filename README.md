# Hikvision Face Recognition Terminal Playground

This repository contains example implementations for connecting to Hikvision face recognition terminals using the ISAPI protocol in multiple programming languages.

## Overview

Hikvision face recognition terminals use the ISAPI (Internet Server Application Programming Interface) protocol for communication. This is a RESTful HTTP-based API that requires digest authentication.

## Supported Languages

- **Python** - Using `requests` library with digest authentication
- **C++** - Using `libcurl` for HTTP requests
- **C#** - Using `HttpClient` with digest authentication

## Common Features

All implementations provide:

- Basic connection testing
- Device information retrieval
- System capabilities query
- Face database capabilities
- HTTP Digest Authentication
- Error handling and logging

## Quick Start

### Python
```bash
cd python
pip install -r requirements.txt
# Update configuration in hikvision_client.py
python hikvision_client.py
```

### C++
```bash
cd cpp
mkdir build && cd build
cmake ..
make
# Update configuration in main.cpp before building
./hikvision_client
```

### C#
```bash
cd csharp
dotnet restore
# Update configuration in Program.cs
dotnet run
```

## Configuration

Before running any implementation, update the device configuration:

- **Device IP**: Replace `*************` with your device's IP address
- **Username**: Replace `admin` with your device username
- **Password**: Replace `password` with your device password
- **Port**: Replace `80` with your device port if different

## Common ISAPI Endpoints

The implementations demonstrate these common endpoints:

- `/ISAPI/System/deviceInfo` - Get basic device information
- `/ISAPI/System/capabilities` - Get system capabilities
- `/ISAPI/AccessControl/FaceDataRecord/capabilities` - Get face database capabilities

## Authentication

Hikvision devices typically use HTTP Digest Authentication. All implementations handle this automatically:

- **Python**: Uses `requests.auth.HTTPDigestAuth`
- **C++**: Uses `libcurl` with `CURLAUTH_DIGEST`
- **C#**: Uses `HttpClientHandler` with `NetworkCredential`

## Network Requirements

- Ensure your device is accessible on the network
- Default port is usually 80 (HTTP) or 443 (HTTPS)
- Some devices may use custom ports (8000, 8080, etc.)
- Firewall rules may need to be configured

## Troubleshooting

### Connection Issues
1. Verify device IP address and port
2. Check network connectivity (`ping` the device)
3. Verify username and password
4. Check if device has ISAPI enabled
5. Ensure device firmware supports the endpoints

### Authentication Issues
1. Verify credentials are correct
2. Check if user has sufficient permissions
3. Some devices require admin privileges for certain endpoints

### API Issues
1. Check device documentation for supported endpoints
2. Verify ISAPI version compatibility
3. Some features may require specific firmware versions

## Device Setup

Before using these clients, ensure your Hikvision device:

1. Has network connectivity
2. Has a user account with appropriate permissions
3. Has ISAPI enabled (usually enabled by default)
4. Has the correct time/date settings
5. Has compatible firmware version

## Security Notes

- Always use HTTPS in production environments
- Use strong passwords for device accounts
- Consider using certificate-based authentication for production
- Regularly update device firmware for security patches
- Implement proper network segmentation

## Contributing

Feel free to contribute improvements or additional language implementations:

1. Fork the repository
2. Create a feature branch
3. Add your implementation following the existing patterns
4. Include proper documentation and error handling
5. Submit a pull request

## License

This project is provided as-is for educational and development purposes. Please ensure compliance with Hikvision's terms of service and your local regulations when using these examples.

## Resources

- [Hikvision Developer Portal](https://www.hikvision.com/en/support/developer/)
- [ISAPI Documentation](https://www.hikvision.com/content/dam/hikvision/en/support/download/sdk/isapi/)
- [HTTP Digest Authentication RFC](https://tools.ietf.org/html/rfc2617)
a test/controlled environment to test out scripts on the ISAPI dev documentation. 
