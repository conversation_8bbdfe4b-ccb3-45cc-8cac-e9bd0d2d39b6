#!/usr/bin/env python3
"""
Hikvision ISAPI Client for Face Recognition Terminals
Basic connection and authentication example
"""

import requests
from requests.auth import HTTPDigestAuth
import json
import xml.etree.ElementTree as ET
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HikvisionClient:
    """
    Basic Hikvision ISAPI client for face recognition terminals
    """
    
    def __init__(self, host: str, username: str, password: str, port: int = 80):
        """
        Initialize the Hikvision client
        
        Args:
            host: IP address or hostname of the device
            username: Username for authentication
            password: Password for authentication  
            port: Port number (default 80)
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.base_url = f"http://{host}:{port}"
        self.auth = HTTPDigestAuth(username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None) -> requests.Response:
        """
        Make HTTP request to the device
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            data: Request data
            headers: Request headers
            
        Returns:
            Response object
        """
        url = f"{self.base_url}{endpoint}"
        
        default_headers = {
            'Content-Type': 'application/xml',
            'Accept': 'application/xml'
        }
        
        if headers:
            default_headers.update(headers)
            
        try:
            response = self.session.request(
                method=method,
                url=url,
                data=data,
                headers=default_headers,
                timeout=10
            )
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test connection to the device
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            response = self._make_request('GET', '/ISAPI/System/deviceInfo')
            logger.info("Connection test successful")
            return True
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def get_device_info(self) -> Optional[Dict[str, Any]]:
        """
        Get device information
        
        Returns:
            Device info dictionary or None if failed
        """
        try:
            response = self._make_request('GET', '/ISAPI/System/deviceInfo')
            
            # Parse XML response
            root = ET.fromstring(response.text)
            device_info = {}
            
            for child in root:
                device_info[child.tag] = child.text
                
            return device_info
            
        except Exception as e:
            logger.error(f"Failed to get device info: {e}")
            return None
    
    def get_system_capabilities(self) -> Optional[Dict[str, Any]]:
        """
        Get system capabilities
        
        Returns:
            Capabilities dictionary or None if failed
        """
        try:
            response = self._make_request('GET', '/ISAPI/System/capabilities')
            
            # Parse XML response
            root = ET.fromstring(response.text)
            capabilities = {}
            
            # Extract capabilities (simplified parsing)
            for child in root:
                capabilities[child.tag] = child.text or {}
                
            return capabilities
            
        except Exception as e:
            logger.error(f"Failed to get capabilities: {e}")
            return None
    
    def get_face_database_list(self) -> Optional[list]:
        """
        Get list of face databases
        
        Returns:
            List of face databases or None if failed
        """
        try:
            response = self._make_request('GET', '/ISAPI/AccessControl/FaceDataRecord/capabilities')
            
            # Parse XML response
            root = ET.fromstring(response.text)
            databases = []
            
            # Extract database information (simplified)
            for db in root.findall('.//FaceDatabase'):
                db_info = {}
                for child in db:
                    db_info[child.tag] = child.text
                databases.append(db_info)
                
            return databases
            
        except Exception as e:
            logger.error(f"Failed to get face databases: {e}")
            return None


def main():
    """
    Example usage of the Hikvision client
    """
    # Configuration - Update these values for your device
    DEVICE_IP = "**************"  # Replace with your device IP
    USERNAME = "admin"           # Replace with your username
    PASSWORD = "admin"        # Replace with your password
    PORT = 80                  # Replace with your port if different
    
    # Create client instance
    client = HikvisionClient(DEVICE_IP, USERNAME, PASSWORD, PORT)
    
    print("=" * 50)
    print("Hikvision Face Recognition Terminal Client")
    print("=" * 50)
    
    # Test connection
    print("\n1. Testing connection...")
    if client.test_connection():
        print("✓ Connection successful!")
    else:
        print("✗ Connection failed!")
        return
    
    # Get device information
    print("\n2. Getting device information...")
    device_info = client.get_device_info()
    if device_info:
        print("Device Info:")
        for key, value in device_info.items():
            print(f"  {key}: {value}")
    else:
        print("✗ Failed to get device info")
    
    # Get system capabilities
    print("\n3. Getting system capabilities...")
    capabilities = client.get_system_capabilities()
    if capabilities:
        print("✓ Capabilities retrieved successfully")
        print(f"Available capabilities: {list(capabilities.keys())}")
    else:
        print("✗ Failed to get capabilities")
    
    # Get face databases
    print("\n4. Getting face databases...")
    databases = client.get_face_database_list()
    if databases:
        print(f"✓ Found {len(databases)} face database(s)")
        for i, db in enumerate(databases):
            print(f"  Database {i+1}: {db}")
    else:
        print("✗ Failed to get face databases")


if __name__ == "__main__":
    main()
