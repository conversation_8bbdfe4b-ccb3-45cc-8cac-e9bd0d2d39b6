#ifndef HIKVISION_CLIENT_H
#define HIKVISION_CLIENT_H

#include <string>
#include <map>
#include <memory>
#include <curl/curl.h>

/**
 * @brief Hikvision ISAPI Client for Face Recognition Terminals
 * 
 * This class provides basic functionality to connect and communicate
 * with Hikvision face recognition terminals using the ISAPI protocol.
 */
class HikvisionClient {
public:
    /**
     * @brief Constructor
     * @param host Device IP address or hostname
     * @param username Username for authentication
     * @param password Password for authentication
     * @param port Port number (default 80)
     */
    HikvisionClient(const std::string& host, const std::string& username, 
                   const std::string& password, int port = 80);
    
    /**
     * @brief Destructor
     */
    ~HikvisionClient();
    
    /**
     * @brief Test connection to the device
     * @return true if connection successful, false otherwise
     */
    bool testConnection();
    
    /**
     * @brief Get device information
     * @return Device information as XML string, empty if failed
     */
    std::string getDeviceInfo();
    
    /**
     * @brief Get system capabilities
     * @return System capabilities as XML string, empty if failed
     */
    std::string getSystemCapabilities();
    
    /**
     * @brief Get face database capabilities
     * @return Face database capabilities as XML string, empty if failed
     */
    std::string getFaceDatabaseCapabilities();

private:
    std::string m_host;
    std::string m_username;
    std::string m_password;
    int m_port;
    std::string m_baseUrl;
    CURL* m_curl;
    
    /**
     * @brief Structure to hold response data
     */
    struct ResponseData {
        std::string data;
        long responseCode;
    };
    
    /**
     * @brief Callback function for writing response data
     */
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, ResponseData* response);
    
    /**
     * @brief Make HTTP request to the device
     * @param method HTTP method (GET, POST, PUT, DELETE)
     * @param endpoint API endpoint
     * @param data Request data (optional)
     * @return Response data structure
     */
    ResponseData makeRequest(const std::string& method, const std::string& endpoint, 
                           const std::string& data = "");
    
    /**
     * @brief Initialize curl handle
     */
    void initializeCurl();
    
    /**
     * @brief Setup authentication for curl
     */
    void setupAuthentication();
};

#endif // HIKVISION_CLIENT_H
