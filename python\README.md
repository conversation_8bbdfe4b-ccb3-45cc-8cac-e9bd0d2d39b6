# Hikvision Python Client

This directory contains a Python implementation for connecting to Hikvision face recognition terminals using the ISAPI protocol.

## Features

- Basic HTTP Digest Authentication
- Device information retrieval
- System capabilities query
- Face database management
- Connection testing

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Update the configuration in `hikvision_client.py`:
```python
DEVICE_IP = "*************"  # Your device IP
USERNAME = "admin"           # Your username
PASSWORD = "password"        # Your password
PORT = 80                   # Your port
```

## Usage

Run the basic example:
```bash
python hikvision_client.py
```

## API Endpoints

The client supports various ISAPI endpoints:

- `/ISAPI/System/deviceInfo` - Get device information
- `/ISAPI/System/capabilities` - Get system capabilities
- `/ISAPI/AccessControl/FaceDataRecord/capabilities` - Get face database capabilities

## Authentication

The client uses HTTP Digest Authentication as required by Hikvision devices. Make sure your device has the correct user credentials configured.

## Error Handling

The client includes basic error handling and logging. Check the console output for connection issues or API errors.
