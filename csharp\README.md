# Hikvision C# Client

This directory contains a C# implementation for connecting to Hikvision face recognition terminals using the ISAPI protocol.

## Features

- HTTP Digest Authentication using HttpClient
- Device information retrieval
- System capabilities query
- Face database capabilities
- XML response parsing and display
- Async/await pattern for non-blocking operations

## Requirements

- .NET 6.0 or later
- System.Net.Http package

## Setup

1. Make sure you have .NET 6.0 SDK installed:
```bash
dotnet --version
```

2. Restore packages:
```bash
dotnet restore
```

## Configuration

Update the configuration in `Program.cs`:
```csharp
private const string DEVICE_IP = "*************";  // Your device IP
private const string USERNAME = "admin";            // Your username
private const string PASSWORD = "password";         // Your password
private const int PORT = 80;                        // Your port
```

## Building

Build the project:
```bash
dotnet build
```

## Usage

Run the application:
```bash
dotnet run
```

## API Endpoints

The client supports various ISAPI endpoints:

- `/ISAPI/System/deviceInfo` - Get device information
- `/ISAPI/System/capabilities` - Get system capabilities
- `/ISAPI/AccessControl/FaceDataRecord/capabilities` - Get face database capabilities

## Authentication

The client uses HTTP Digest Authentication as required by Hikvision devices. The authentication is handled automatically by the HttpClientHandler with NetworkCredential.

## Features

- **Async Operations**: All network operations are asynchronous
- **XML Parsing**: Automatic parsing and formatted display of XML responses
- **Error Handling**: Comprehensive error handling with detailed messages
- **Resource Management**: Proper disposal of HttpClient resources
