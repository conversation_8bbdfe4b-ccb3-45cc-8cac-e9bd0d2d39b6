# Hikvision C++ Client

This directory contains a C++ implementation for connecting to Hikvision face recognition terminals using the ISAPI protocol.

## Features

- HTTP Digest Authentication using libcurl
- Device information retrieval
- System capabilities query
- Face database capabilities
- Connection testing

## Dependencies

- libcurl (for HTTP requests)
- CMake (for building)
- C++11 compatible compiler

## Setup

### On Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install libcurl4-openssl-dev cmake build-essential
```

### On Windows:
- Install vcpkg and libcurl
- Or use Visual Studio with vcpkg integration

### On macOS:
```bash
brew install curl cmake
```

## Building

1. Create build directory:
```bash
mkdir build
cd build
```

2. Configure with CMake:
```bash
cmake ..
```

3. Build:
```bash
make
```

## Configuration

Update the configuration in `main.cpp`:
```cpp
const std::string DEVICE_IP = "*************";  // Your device IP
const std::string USERNAME = "admin";            // Your username
const std::string PASSWORD = "password";         // Your password
const int PORT = 80;                            // Your port
```

## Usage

Run the compiled executable:
```bash
./hikvision_client
```

## API Endpoints

The client supports various ISAPI endpoints:

- `/ISAPI/System/deviceInfo` - Get device information
- `/ISAPI/System/capabilities` - Get system capabilities
- `/ISAPI/AccessControl/FaceDataRecord/capabilities` - Get face database capabilities

## Authentication

The client uses HTTP Digest Authentication as required by Hikvision devices using libcurl's built-in authentication support.
