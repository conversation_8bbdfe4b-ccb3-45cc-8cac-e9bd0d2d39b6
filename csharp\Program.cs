using System;
using System.Threading.Tasks;

namespace HikvisionPlayground
{
    class Program
    {
        // Configuration - Update these values for your device
        private const string DEVICE_IP = "*************";  // Replace with your device IP
        private const string USERNAME = "admin";            // Replace with your username
        private const string PASSWORD = "password";         // Replace with your password
        private const int PORT = 80;                        // Replace with your port if different

        static async Task Main(string[] args)
        {
            Console.WriteLine("==================================================");
            Console.WriteLine("Hikvision Face Recognition Terminal Client (C#)");
            Console.WriteLine("==================================================");

            try
            {
                // Create client instance
                using (var client = new HikvisionClient(DEVICE_IP, USERNAME, PASSWORD, PORT))
                {
                    // Test connection
                    Console.WriteLine("\n1. Testing connection...");
                    bool connectionSuccess = await client.TestConnectionAsync();
                    
                    if (!connectionSuccess)
                    {
                        Console.WriteLine("Connection failed! Please check your configuration.");
                        Console.WriteLine("Press any key to exit...");
                        Console.ReadKey();
                        return;
                    }

                    // Get device information
                    Console.WriteLine("\n2. Getting device information...");
                    string deviceInfo = await client.GetDeviceInfoAsync();
                    client.DisplayXmlInfo(deviceInfo, "Device Information");

                    // Get system capabilities
                    Console.WriteLine("\n3. Getting system capabilities...");
                    string capabilities = await client.GetSystemCapabilitiesAsync();
                    client.DisplayXmlInfo(capabilities, "System Capabilities");

                    // Get face database capabilities
                    Console.WriteLine("\n4. Getting face database capabilities...");
                    string faceCapabilities = await client.GetFaceDatabaseCapabilitiesAsync();
                    client.DisplayXmlInfo(faceCapabilities, "Face Database Capabilities");

                    Console.WriteLine("\nAll operations completed successfully!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
